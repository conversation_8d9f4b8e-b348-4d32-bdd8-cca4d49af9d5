module.exports = {
  apps: [
    {
      name: 'products-backend',
      script: 'dist/app.js',
      cwd: './products-backend',
      instances: 1,
      exec_mode: 'fork',
      
      env: {
        NODE_ENV: 'production',
        PORT: 3002
      },
      
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      restart_delay: 4000,
      
      log_file: './products-backend/logs/combined.log',
      out_file: './products-backend/logs/out.log',
      error_file: './products-backend/logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      
      kill_timeout: 5000,
      listen_timeout: 3000
    }
  ]
};
